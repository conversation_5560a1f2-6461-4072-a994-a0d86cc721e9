# 开发环境配置
spring:
  datasource:
    dynamic:
      datasource:
        # 主数据源 - 开发环境
        master:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: jdbc:mysql://************:3306/taiherenyi?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
          username: taiherenyi
          password: rXBGp54HzcYewSpe
          # Druid 连接池配置
          druid:
            # 初始连接数
            initial-size: 5
            # 最小连接池数量
            min-idle: 10
            # 最大连接池数量
            max-active: 20
            # 配置获取连接等待超时的时间
            max-wait: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            time-between-eviction-runs-millis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            min-evictable-idle-time-millis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            max-evictable-idle-time-millis: 900000
            # 配置检测连接是否有效
            validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            # 打开PSCache，并且指定每个连接上PSCache的大小
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
        # 从数据源1 - 读库
        # slave_1:
        #   type: com.alibaba.druid.pool.DruidDataSource
        #   driver-class-name: com.mysql.cj.jdbc.Driver
        #   url: *********************************************************************************************************************************************
        #   username: root
        #   password: 123456
        #   druid:
        #     initial-size: 5
        #     min-idle: 5
        #     max-active: 10
        #     max-wait: 60000
        #     time-between-eviction-runs-millis: 60000
        #     min-evictable-idle-time-millis: 300000
        #     max-evictable-idle-time-millis: 900000
        #     validation-query: SELECT 1 FROM DUAL
        #     test-while-idle: true
        #     test-on-borrow: false
        #     test-on-return: false
# Redis配置
  redis:
    # Redis连接地址
    host: ************
    # Redis连接端口
    port: 6379
    # Redis数据库索引（默认为0）
    database: 7
    # Redis连接密码（如果设置了密码）
    password:
    # 连接超时时间（毫秒）
    timeout: 3000
    # Lettuce连接池配置
    lettuce:
      pool:
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 20
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 5
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
# 开发环境日志配置 - 日志格式已在 logback-spring.xml 中配置
logging:
  level:
    # 这些配置会与 logback-spring.xml 中的配置合并
    com.zsm.mapper: debug
    com.baomidou.dynamic.datasource: debug
    druid.sql.Statement: debug
    root: info

# 开发环境 Swagger 配置
springdoc:
  swagger-ui:
    enabled: true  # 开发环境启用 Swagger UI

# 数据库初始化配置
app:
  database:
    auto-init: true  # 开发环境启用自动初始化数据库表

# 杭创接口配置 - 开发环境
hangchuang:
  # 服务器地址配置
  server: http://************:720
  # HIS接口服务器地址配置
  his-server: http://************:605


