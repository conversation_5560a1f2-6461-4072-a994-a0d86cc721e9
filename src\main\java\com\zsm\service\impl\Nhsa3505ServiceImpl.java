package com.zsm.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zsm.common.SaasAuthorizationVerifyAspect;
import com.zsm.config.HangChuangConfig;
import com.zsm.constant.NhsaAccountConstant;
import com.zsm.entity.Nhsa3505;
import com.zsm.entity.YsfStoDps;
import com.zsm.entity.YsfStoDpsSub;
import com.zsm.entity.YsfStoTcTask;
import com.zsm.mapper.Nhsa3505Mapper;
import com.zsm.mapper.YsfStoDpsSubMapper;
import com.zsm.mapper.YsfStoTcTaskMapper;
import com.zsm.model.ApiResult;
import com.zsm.model.domain.NhsaAccount;
import com.zsm.model.dto.OutpatientPrescriptionQueryDto;
import com.zsm.model.enums.SdDpsEnum;
import com.zsm.model.enums.TaskStatusEnum;
import com.zsm.model.nhsa.request.DrugTracInfo;
import com.zsm.model.nhsa.request.fsi3505.Selinfo3505;
import com.zsm.model.nhsa.response.NhsaCityResponse;
import com.zsm.model.vo.*;
import com.zsm.service.Nhsa3505Service;
import com.zsm.service.YsfStoDpsService;
import com.zsm.service.YsfStoTcTaskService;
import com.zsm.utils.*;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 3505销售记录报文表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-02
 */
@Slf4j
@Service
public class Nhsa3505ServiceImpl extends ServiceImpl<Nhsa3505Mapper, Nhsa3505> implements Nhsa3505Service {

    @Resource
    private YsfStoDpsSubMapper ysfStoDpsSubMapper;
    @Resource
    private YsfStoTcTaskMapper ysfStoTcTaskMapper;
    @Resource
    private YsfStoTcTaskService ysfStoTcTaskService;
    @Resource
    private YsfStoDpsService ysfStoDpsService;
    @Resource
    private HangChuangConfig hangChuangConfig;

    @Async
    @Override
    public void save3505Async(List<OutpatientPrescriptionResponseVo.PrescriptionItem> dataList) {
        if (dataList == null || dataList.isEmpty()) {
            log.warn("处方药品数据列表为空，跳过保存操作");
            return;
        }

        log.info("开始异步保存3505销售记录，总数据量: {}", dataList.size());

        try {
            List<Nhsa3505> saveList = new ArrayList<>();
            int skipCount = 0;
            Set<String> processedKeys = new HashSet<>(); // 用于去重当前批次中的重复数据

            for (OutpatientPrescriptionResponseVo.PrescriptionItem item : dataList) {
                // 检查必要字段,只保存西药
                String[] arr = {"X", "Z"};
                boolean medListCodgValid = false;
                if (StringUtils.hasText(item.getMed_list_codg())) {
                    for (String prefix : arr) {
                        if (item.getMed_list_codg().startsWith(prefix)) {
                            medListCodgValid = true;
                            break;
                        }
                    }
                }
                if (!StringUtils.hasText(item.getFixmedins_bchno()) || !medListCodgValid) {
                    log.warn("定点医疗机构批次号为空或药品编码不符合要求，跳过该条记录: {}", item);
                    skipCount++;
                    continue;
                }

                // 获取医疗机构编码
                String medicalCode = NhsaAccountConstant.getNhsaAccount().getMedicalCode();
                String fixmedinsBchno = item.getFixmedins_bchno();

                // 构建唯一键（与数据库唯一约束保持一致）
                String uniqueKey = medicalCode + "-" + fixmedinsBchno;

                // 检查当前批次中是否已处理过相同的记录
                if (processedKeys.contains(uniqueKey)) {
                    log.debug("当前批次中已存在相同记录，跳过保存 uniqueKey: {}", uniqueKey);
                    skipCount++;
                    continue;
                }

                // 通过medical_code和fixmedins_bchno组合查询是否已存在记录（与数据库唯一约束保持一致）
                LambdaQueryWrapper<Nhsa3505> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Nhsa3505::getMedicalCode, medicalCode)
                        .eq(Nhsa3505::getFixmedinsBchno, fixmedinsBchno);

                if (count(queryWrapper) > 0) {
                    log.debug("数据库中已存在记录，跳过保存 medical_code: {}, fixmedins_bchno: {}",
                            medicalCode, fixmedinsBchno);
                    skipCount++;
                    continue;
                }

                // 转换为Nhsa3505实体
                Nhsa3505 entity = convertToNhsa3505(item);
                if (entity != null) {
                    saveList.add(entity);
                    processedKeys.add(uniqueKey); // 记录已处理的唯一键
                }
            }

            // 使用逐条保存的方式，避免批量保存时的唯一键冲突
            if (!saveList.isEmpty()) {
                int successCount = 0;
                int failCount = 0;

                for (Nhsa3505 entity : saveList) {
                    try {
                        // 再次检查是否存在（防止并发插入）
                        LambdaQueryWrapper<Nhsa3505> checkWrapper = new LambdaQueryWrapper<>();
                        checkWrapper.eq(Nhsa3505::getMedicalCode, entity.getMedicalCode())
                                .eq(Nhsa3505::getFixmedinsBchno, entity.getFixmedinsBchno());

                        if (count(checkWrapper) == 0) {
                            boolean result = save(entity);
                            if (result) {
                                successCount++;
                            } else {
                                failCount++;
                                log.error("保存门诊处方记录失败，medical_code: {}, fixmedins_bchno: {}",
                                        entity.getMedicalCode(), entity.getFixmedinsBchno());
                            }
                        } else {
                            log.debug("并发检查发现记录已存在，跳过保存 medical_code: {}, fixmedins_bchno: {}",
                                    entity.getMedicalCode(), entity.getFixmedinsBchno());
                            skipCount++;
                        }
                    } catch (Exception e) {
                        failCount++;
                        // 检查是否是唯一键冲突异常
                        if (e.getMessage() != null && e.getMessage().contains("Duplicate entry")) {
                            log.debug("唯一键冲突，记录可能已存在，跳过该条记录: medical_code={}, fixmedins_bchno={}",
                                    entity.getMedicalCode(), entity.getFixmedinsBchno());
                            skipCount++;
                        } else {
                            log.error("保存门诊处方记录异常，medical_code: {}, fixmedins_bchno: {}",
                                    entity.getMedicalCode(), entity.getFixmedinsBchno(), e);
                        }
                    }
                }

                log.info("3505销售记录保存完成，成功: {}, 失败: {}, 跳过: {}",
                        successCount, failCount, skipCount);
            } else {
                log.info("没有新的记录需要保存，跳过数量: {}", skipCount);
            }

        } catch (Exception e) {
            log.error("异步保存3505销售记录发生异常", e);
        }
    }

    @Override
    public void updateDrugTraceabilityInfo(String outPresdetailid, String drugtracinfo, String userName, String orgId, String orgName) {
        if (!StringUtils.hasText(outPresdetailid) || !StringUtils.hasText(drugtracinfo)) {
            log.warn("更新追溯信息时，outPresdetailid或drugtracinfo为空");
            return;
        }

        // 查询对应的Nhsa3505记录
        LambdaQueryWrapper<Nhsa3505> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Nhsa3505::getCfmxxh, outPresdetailid);

        Nhsa3505 nhsa3505 = getOne(queryWrapper);
        if (nhsa3505 == null) {
            log.error("未找到对应的Nhsa3505记录，outPresdetailid: {}", outPresdetailid);
            return;
        }

        // 更新追溯码信息
        nhsa3505.setDrugTracInfo(drugtracinfo);

        boolean updated = updateById(nhsa3505);
        if (updated) {
            log.info("成功更新Nhsa3505记录，outPresdetailid: {}, drugtracinfo: {}", outPresdetailid, drugtracinfo);
        } else {
            log.error("更新Nhsa3505记录失败，outPresdetailid: {}", outPresdetailid);
        }
    }

    @Override
    public void updateDrugTraceabilityInfo(String fixmedinsBchno, String drugtracinfo) {
        if (!StringUtils.hasText(fixmedinsBchno) || !StringUtils.hasText(drugtracinfo)) {
            log.warn("更新追溯信息时，outPresdetailid或drugtracinfo为空");
            return;
        }

        // 查询对应的Nhsa3505记录
        LambdaQueryWrapper<Nhsa3505> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Nhsa3505::getFixmedinsBchno, fixmedinsBchno);

        Nhsa3505 nhsa3505 = getOne(queryWrapper);
        if (nhsa3505 == null) {
            log.error("未找到对应的Nhsa3505记录，fixmedinsBchno: {}", fixmedinsBchno);
            return;
        }

        // 更新追溯码信息
        nhsa3505.setDrugTracInfo(drugtracinfo);

        boolean updated = updateById(nhsa3505);
        if (updated) {
            log.info("成功更新Nhsa3505记录，fixmedinsBchno: {}, drugtracinfo: {}", fixmedinsBchno, drugtracinfo);
        } else {
            log.error("更新Nhsa3505记录失败，fixmedinsBchno: {}", fixmedinsBchno);
        }
    }

    @Async
    @Override
    public void saveInpatientDataToNhsa3505Async(List<InPatientDispenseDetailBindScatteredVo> inpatientList) {
        if (inpatientList == null || inpatientList.isEmpty()) {
            log.warn("住院发药数据列表为空，跳过保存操作");
            return;
        }

        log.info("开始异步保存住院发药数据到nhsa_3505表，总数据量: {}", inpatientList.size());

        // 检查当前线程上下文中是否有用户信息
        if (!UserContextUtil.hasCurrentUser()) {
            log.error("异步线程中未找到用户信息，可能影响数据保存时的用户字段设置");
        }

        try {
            List<Nhsa3505> saveList = new ArrayList<>();
            int skipCount = 0;
            Set<String> processedKeys = new HashSet<>(); // 用于去重当前批次中的重复数据

            for (InPatientDispenseDetailBindScatteredVo item : inpatientList) {
                // 检查必要字段,只保存西药
                String[] arr = {"X", "Z"};
                boolean medListCodgValid = false;
                if (StringUtils.hasText(item.getMedListCodg())) {
                    for (String prefix : arr) {
                        if (item.getMedListCodg().startsWith(prefix)) {
                            medListCodgValid = true;
                            break;
                        }
                    }
                }
                if (!StringUtils.hasText(item.getFixmedinsBchno()) || !medListCodgValid) {
                    log.warn("定点医疗机构批次号为空或药品编码不符合要求，跳过该条记录: {}", JSONUtil.toJsonStr(item));
                    skipCount++;
                    continue;
                }

                // 获取医疗机构编码
                String medicalCode = NhsaAccountConstant.getNhsaAccount().getMedicalCode();
                String fixmedinsBchno = item.getFixmedinsBchno();

                // 构建唯一键（与数据库唯一约束保持一致）
                String uniqueKey = medicalCode + "-" + fixmedinsBchno;

                // 检查当前批次中是否已处理过相同的记录
                if (processedKeys.contains(uniqueKey)) {
                    log.debug("当前批次中已存在相同记录，跳过保存 uniqueKey: {}", uniqueKey);
                    skipCount++;
                    continue;
                }

                // 通过medical_code和fixmedins_bchno组合查询是否已存在记录（与数据库唯一约束保持一致）
                LambdaQueryWrapper<Nhsa3505> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Nhsa3505::getMedicalCode, medicalCode)
                        .eq(Nhsa3505::getFixmedinsBchno, fixmedinsBchno);

                if (count(queryWrapper) > 0) {
                    log.debug("数据库中已存在记录，跳过保存 medical_code: {}, fixmedins_bchno: {}",
                            medicalCode, fixmedinsBchno);
                    skipCount++;
                    continue;
                }

                // 转换为Nhsa3505实体
                Nhsa3505 entity = convertInpatientToNhsa3505(item);
                if (entity != null) {
                    saveList.add(entity);
                    processedKeys.add(uniqueKey); // 记录已处理的唯一键
                }
            }

            // 使用逐条保存的方式，避免批量保存时的唯一键冲突
            if (!saveList.isEmpty()) {
                int successCount = 0;
                int failCount = 0;

                for (Nhsa3505 entity : saveList) {
                    try {
                        // 再次检查是否存在（防止并发插入）
                        LambdaQueryWrapper<Nhsa3505> checkWrapper = new LambdaQueryWrapper<>();
                        checkWrapper.eq(Nhsa3505::getMedicalCode, entity.getMedicalCode())
                                .eq(Nhsa3505::getFixmedinsBchno, entity.getFixmedinsBchno());

                        if (count(checkWrapper) == 0) {
                            boolean result = save(entity);
                            if (result) {
                                successCount++;
                            } else {
                                failCount++;
                                log.error("保存住院发药记录失败，medical_code: {}, fixmedins_bchno: {}",
                                        entity.getMedicalCode(), entity.getFixmedinsBchno());
                            }
                        } else {
                            log.debug("并发检查发现记录已存在，跳过保存 medical_code: {}, fixmedins_bchno: {}",
                                    entity.getMedicalCode(), entity.getFixmedinsBchno());
                            skipCount++;
                        }
                    } catch (Exception e) {
                        failCount++;
                        // 检查是否是唯一键冲突异常
                        if (e.getMessage() != null && e.getMessage().contains("Duplicate entry")) {
                            log.debug("唯一键冲突，记录可能已存在，跳过该条记录: medical_code={}, fixmedins_bchno={}",
                                    entity.getMedicalCode(), entity.getFixmedinsBchno());
                            skipCount++;
                        } else {
                            log.error("保存住院发药记录异常，medical_code: {}, fixmedins_bchno: {}",
                                    entity.getMedicalCode(), entity.getFixmedinsBchno(), e);
                        }
                    }
                }

                log.info("住院发药数据保存到nhsa_3505完成，成功: {}, 失败: {}, 跳过: {}",
                        successCount, failCount, skipCount);
            } else {
                log.info("没有新的住院发药记录需要保存，跳过数量: {}", skipCount);
            }

        } catch (Exception e) {
            log.error("异步保存住院发药数据到nhsa_3505表发生异常", e);
        }
    }

    @Async
    @Override
    public void saveInpatientDataToNhsa3505AsyncWithContext(List<InPatientDispenseDetailBindScatteredVo> inpatientList, SaasUserInfoResponse userInfo) {
        // 在异步线程中设置用户上下文
        UserContextUtil.runWithUserContext(userInfo, () -> {
            log.info("在异步线程中设置用户上下文，用户：{}",
                    userInfo != null && userInfo.getUser() != null ? userInfo.getUser().getUserName() : "未知用户");
            // 调用原有的异步保存方法
            saveInpatientDataToNhsa3505Async(inpatientList);
        });
    }

    /**
     * 将PrescriptionItem转换为Nhsa3505实体
     */
    private Nhsa3505 convertToNhsa3505(OutpatientPrescriptionResponseVo.PrescriptionItem item) {
        try {
            Nhsa3505 entity = new Nhsa3505();

            entity.setMedicalCode(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            entity.setMedicalName(NhsaAccountConstant.getNhsaAccount().getMedicalName());

            // 基本药品信息
            entity.setMedListCodg(item.getMed_list_codg());
            entity.setFixmedinsHilistId(item.getFixmedins_hilist_id());
            entity.setFixmedinsHilistName(item.getFixmedins_hilist_name());
            entity.setFixmedinsBchno(item.getFixmedins_bchno());

            // 医师信息
            entity.setPrscDrCertno(item.getPrsc_dr_certno());
            entity.setPrscDrName(item.getPrsc_dr_name());

            // 药师信息
            entity.setPharCertno(item.getPhar_certno());
            entity.setPharName(item.getPhar_name());
            entity.setPharPracCertNo(item.getPhar_prac_cert_no());

            // 患者就诊信息
            entity.setMdtrtSn(item.getMdtrt_sn());
            entity.setPsnName(item.getPsn_name());

            // 生产信息
            entity.setManuLotnum(item.getManu_lotnum());
            if (!StringUtils.hasText(item.getManu_lotnum())) {
                entity.setManuLotnum("-");
            }
            if (StringUtils.hasText(item.getManu_date())) {
                entity.setManuDate(DateUtils.parseDate(item.getManu_date()));
            }
            if (StringUtils.hasText(item.getExpy_end())) {
                entity.setExpyEnd(DateUtils.parseDate(item.getExpy_end()));
            }

            // 标志信息
            entity.setRxFlag(item.getRx_flag() != null ? String.valueOf(item.getRx_flag()) : null);
            entity.setTrdnFlag(item.getTrdn_flag() != null ? String.valueOf(item.getTrdn_flag()) : null);

            // 处方信息
            entity.setRxno(item.getRxno());
            entity.setRxCircFlag(item.getRx_circ_flag());
            entity.setRtalDocno(item.getRtal_docno());
            entity.setStooutNo(item.getStoout_no());
            entity.setBchno(item.getBchno());

            // 销售信息
            if (StringUtils.hasText(item.getSel_retn_cnt())) {
                entity.setSelRetnCnt(new BigDecimal(item.getSel_retn_cnt()));
            }
            if (StringUtils.hasText(item.getMin_sel_retn_cnt())) {
                entity.setMinUnitSelRetnCnt(new BigDecimal(item.getMin_sel_retn_cnt()));
            }
            if (StringUtils.hasText(item.getSel_retn_time())) {
                entity.setSelRetnTime(DateUtils.parseDateTime(item.getSel_retn_time()));
            }
            entity.setSelRetnOpterName(item.getSel_retn_opter_name());
            entity.setMdtrtSetlType(item.getMdtrt_setl_type() != null ? String.valueOf(item.getMdtrt_setl_type()) : null);

            // 生产企业信息
            entity.setHisEntpName(item.getProdentp_name());

            // 处方序号信息
            entity.setCfxh(item.getCfxh());
            entity.setCfmxxh(item.getCfmxxh());
            entity.setSjh(item.getSjh());
            entity.setPatientId(item.getPatient_id());

            // 规格信息存储到备注字段
            if (StringUtils.hasText(item.getSpec())) {
                entity.setMemo(item.getSpec());
            }

            // 发送和退货信息
            if (StringUtils.hasText(item.getReturn_time())) {
                entity.setReturnTime(DateUtils.parseDateTime(item.getReturn_time()));
            }

            // 系统信息
            LocalDateTime now = LocalDateTime.now();
            entity.setCreateTime(now);
            entity.setCreateDate(now.toLocalDate());
            entity.setDeleteFlag("0");
            entity.setHsaSyncStatus("0"); // 未同步状态

            // 设置必填字段的默认值（如果为空）
            if (!StringUtils.hasText(entity.getMedicalName())) {
                entity.setMedicalName("默认医疗机构");
            }
            if (!StringUtils.hasText(entity.getFixmedinsHilistName())) {
                entity.setFixmedinsHilistName("未知药品");
            }
            if (!StringUtils.hasText(entity.getPrscDrName())) {
                entity.setPrscDrName("未知医师");
            }
            if (!StringUtils.hasText(entity.getPharName())) {
                entity.setPharName("未知药师");
            }
            if (!StringUtils.hasText(entity.getPsnName())) {
                entity.setPsnName("未知患者");
            }
            if (!StringUtils.hasText(entity.getSelRetnOpterName())) {
                entity.setSelRetnOpterName("系统");
            }
            if (!StringUtils.hasText(entity.getHisEntpName())) {
                entity.setHisEntpName("未知生产企业");
            }
            if (!StringUtils.hasText(entity.getHisUniqueKey())) {
                entity.setHisUniqueKey(entity.getFixmedinsBchno());
            }
            if (!StringUtils.hasText(entity.getCompositeKey())) {
                entity.setCompositeKey(entity.getFixmedinsBchno());
            }
            // 根据token获取用户信息,设置所属的用户信息,方便统计药房扫码数据信息
            SaasUserInfoResponse userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
            entity.setYmfUserName(userInfo.getUser().getUserName());
            entity.setYmfNickName(userInfo.getUser().getNickName());
            entity.setYmfUserId(userInfo.getUser().getUserId());

            entity.setSdDps(SdDpsEnum.OUTPATIENT);

            return entity;

        } catch (Exception e) {
            log.error("转换PrescriptionItem到Nhsa3505实体时发生异常: {}", item, e);
            return null;
        }
    }

    /**
     * 将住院发药数据转换为Nhsa3505实体
     * 包含完整的字段映射，避免数据丢失
     *
     * @param item 住院发药数据
     * @return Nhsa3505实体
     */
    private Nhsa3505 convertInpatientToNhsa3505(InPatientDispenseDetailBindScatteredVo item) {
        try {
            Nhsa3505 entity = new Nhsa3505();

            // 医疗机构信息
            entity.setMedicalCode(NhsaAccountConstant.getNhsaAccount().getMedicalCode());
            entity.setMedicalName(NhsaAccountConstant.getNhsaAccount().getMedicalName());

            // 基本药品信息
            entity.setMedListCodg(item.getMedListCodg());
            entity.setFixmedinsHilistId(item.getFixmedinsHilistId());
            entity.setFixmedinsHilistName(item.getFixmedinsHilistName());
            entity.setFixmedinsBchno(item.getFixmedinsBchno());

            // 医师信息
            entity.setPrscDrCertno(item.getPrscDrCertno());
            entity.setPrscDrName(item.getPrscDrName());

            // 药师信息
            entity.setPharCertno(item.getPharCertno());
            entity.setPharName(item.getPharName());
            entity.setPharPracCertNo(item.getPharPracCertNo());

            // 患者就诊信息
            entity.setMdtrtSn(item.getMdtrtSn());
            entity.setPsnName(item.getPsnName());

            // 住院特有字段映射
            entity.setPatientId(item.getPatInHosId()); // 住院号映射到患者ID
            entity.setHisDrugId(item.getHisDrugCode()); // HIS药品编码
            entity.setFeedetlSn(item.getIdFee()); // 费用明细ID

            // 生产信息
            entity.setManuLotnum(item.getManuLotnum());
            if (StringUtils.hasText(item.getManuLotnum())) {
                entity.setManuLotnum(item.getManuLotnum());
            }
            if (StrUtil.isNotBlank(item.getManuDate())) {
                entity.setManuDate(DateUtils.parseDate(item.getManuDate()));
            }
            if (StrUtil.isNotBlank(item.getExpyEnd())) {
                entity.setExpyEnd(DateUtils.parseDate(item.getExpyEnd()));
            }

            // 标志信息
            entity.setRxFlag(item.getRxFlag());
            entity.setTrdnFlag(item.getTrdnFlag());

            // 处方信息 - 住院使用零售单据号作为处方号
            entity.setRxno(item.getRtalDocno());
            entity.setRxCircFlag("0"); // 住院默认为非外购处方
            entity.setRtalDocno(item.getRtalDocno());
            entity.setStooutNo(item.getStooutNo());
            entity.setBchno(item.getBchno());

            // 销售信息
            if (item.getSelRetnCnt() != null) {
                entity.setSelRetnCnt(new BigDecimal(item.getSelRetnCnt()));
                entity.setMinUnitSelRetnCnt(new BigDecimal(item.getSelRetnCnt()));
            }
            if (StrUtil.isNotBlank(item.getSelRetnTime())) {
                entity.setSelRetnTime(DateUtils.parseDateTime(item.getSelRetnTime()));
            }

            // 就诊结算类型
            entity.setMdtrtSetlType(item.getMdtrtSetlType());

            // 生产企业信息
            entity.setHisEntpName(item.getProdentpName());

            // 处方序号信息
            entity.setCfxh(item.getRecordId());
            entity.setCfmxxh(item.getRecordDetailId());

            // 将规格信息存储到备注字段（Nhsa3505实体中没有spec字段）
            if (StrUtil.isNotBlank(item.getSpec())) {
                entity.setMemo("规格: " + item.getSpec());
            }

            // 追溯码相关信息
            if (item.getDrugCode() != null) {
                entity.setDrugProdBarc(item.getDrugCode()); // 使用药品条形码字段存储
            }
            if (item.getDrugTracCodgs() != null && !item.getDrugTracCodgs().isEmpty()) {
                entity.setDrugTracInfo(String.join(",", item.getDrugTracCodgs()));
            }

            // 设置操作人员为药师
            entity.setSelRetnOpterName(StrUtil.isNotBlank(item.getPharName()) ? item.getPharName() : "系统");

            // 系统信息
            LocalDateTime now = LocalDateTime.now();
            entity.setCreateTime(now);
            entity.setCreateDate(now.toLocalDate());
            entity.setDeleteFlag("0");
            entity.setHsaSyncStatus("0"); // 未同步状态

            // 设置必填字段的默认值（如果为空）
            if (StrUtil.isBlank(entity.getMedicalName())) {
                entity.setMedicalName("太和人医");
            }
            if (StrUtil.isBlank(entity.getFixmedinsHilistName())) {
                entity.setFixmedinsHilistName(StrUtil.isNotBlank(item.getNaFee()) ? item.getNaFee() : "未知药品");
            }
            if (StrUtil.isBlank(entity.getPrscDrName())) {
                entity.setPrscDrName("未知医师");
            }
            if (StrUtil.isBlank(entity.getPharName())) {
                entity.setPharName("未知药师");
            }
            if (StrUtil.isBlank(entity.getPsnName())) {
                entity.setPsnName("未知患者");
            }
            if (StrUtil.isBlank(entity.getHisEntpName())) {
                entity.setHisEntpName("未知生产企业");
            }
            if (StrUtil.isBlank(entity.getHisUniqueKey())) {
                entity.setHisUniqueKey(entity.getFixmedinsBchno());
            }
            if (StrUtil.isBlank(entity.getCompositeKey())) {
                entity.setCompositeKey(entity.getFixmedinsBchno());
            }

            // 根据token获取用户信息
            // 优先从UserContextUtil获取用户信息，如果获取不到则从SaasAuthorizationVerifyAspect获取
            SaasUserInfoResponse userInfo = UserContextUtil.getCurrentUser();
            if (userInfo == null) {
                userInfo = SaasAuthorizationVerifyAspect.userInfoThreadLocal.get();
                log.info("从UserContextUtil获取用户信息失败，回退到SaasAuthorizationVerifyAspect:{}", JSONUtil.toJsonStr(userInfo));
            } else {
                log.info("成功从UserContextUtil获取用户信息:{}", JSONUtil.toJsonStr(userInfo));
            }

            if (userInfo != null && userInfo.getUser() != null) {
                entity.setYmfUserName(userInfo.getUser().getUserName());
                entity.setYmfNickName(userInfo.getUser().getNickName());
                entity.setYmfUserId(userInfo.getUser().getUserId());
                log.info("设置用户信息到3505实体：{}", userInfo.getUser().getUserName());
            } else {
                log.error("无法获取用户信息，将使用默认值");
            }
            entity.setSdDps(SdDpsEnum.INPATIENT);

            return entity;

        } catch (Exception e) {
            log.error("转换住院发药数据到Nhsa3505实体时发生异常: recordDetailId={}", item.getRecordDetailId(), e);
            return null;
        }
    }

    /**
     * 将数据上传到两定接口平台
     */
    public void uploadDataToPlatform() {
        try {
            // 同步追溯码信息
            syncDrugTraceInfo();

            // 查询需要上传的数据
            List<Nhsa3505> dataList = queryDataToUpload(null, null);
            if (ObjectUtils.isEmpty(dataList)) {
                log.info("没有需要上传的数据");
                return;
            }
            // 获取签名号
            String signNo = NhsaHttpUtil.getSignNo(NhsaAccountConstant.getNhsaAccount());
            // 处理上传逻辑
            processUpload(dataList, false, signNo);

        } catch (Exception e) {
            log.error("上传数据到两定接口平台时发生异常", e);
        }
    }

    @Override
    public void syncDispenseTime() {

        // 查询任务
        List<YsfStoDpsTaskVo> taskList = ysfStoTcTaskMapper.selectDpsInfoByPendingTask();
        log.info("获取到待同步的任务数:{},详细数据:{}", taskList.size(), JSONUtil.toJsonStr(taskList));
        for (YsfStoDpsTaskVo task : taskList) {
            String cfxh = task.getCfxh();
            // 判断任务是否在8小时内
            if (task.getCreateTime()
                    .getTime() > DateUtil.offsetHour(DateUtil.date(), 8)
                    .getTime()) {
                log.info("任务创建时间超过8小时, task: {}", task);
                // 修改任务状态
                ysfStoTcTaskService.lambdaUpdate()
                        .eq(YsfStoTcTask::getCdBiz, cfxh)
                        .eq(YsfStoTcTask::getDelFlag, "0")
                        .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.EXPIRED.getCode())
                        .update();
                continue;
            }

            // 调用远程东华接口获取处方信息
            OutpatientPrescriptionQueryDto queryDto = new OutpatientPrescriptionQueryDto();
            queryDto.setCfxh(cfxh);
            queryDto.setSend_flag("1"); // 只查询已发药的处方

            // 解析响应数据
            final SoapResponseVo mes0271 = SoapUtil.callSoapServiceWithParams("MES0271", queryDto);
            JSONObject jsonResult = mes0271.getJsonResult();
            if (jsonResult.getInt("code") != 0) {
                log.error("调用东华接口获取处方信息失败,code: {},message: {}", jsonResult.getInt("code"), jsonResult.getStr("message"));
                continue;
            }


            OutpatientPrescriptionResponseVo prescriptionResponse = SoapUtil.parseOutpatientResponseData(mes0271);
            final List<OutpatientPrescriptionResponseVo.PrescriptionItem> dataList = prescriptionResponse.getDataList();


            if (dataList != null && !dataList.isEmpty()) {
                for (OutpatientPrescriptionResponseVo.PrescriptionItem drugDispenseInfo : dataList) {
                    if (drugDispenseInfo.getCfxh()
                            .equals(cfxh) && ObjectUtil.isNotNull(drugDispenseInfo.getSend_time())) {
                        String sendTime = drugDispenseInfo.getSend_time();

                        // 修改发药表的发药时间,发药状态
                        ysfStoDpsService.lambdaUpdate()
                                .eq(YsfStoDps::getCfxh, cfxh)
                                .eq(YsfStoDps::getDelFlag, "0")
                                .set(YsfStoDps::getSendTime, sendTime)
                                .set(YsfStoDps::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                                .update();

                        // 修改任务表的任务状态
                        ysfStoTcTaskService.lambdaUpdate()
                                .eq(YsfStoTcTask::getCdBiz, cfxh)
                                .eq(YsfStoTcTask::getDelFlag, "0")
                                .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                                .update();
                    }
                }
            }
        }
    }


    @Override
    public ApiResult<String> manualUploadDataToPlatform(String cfmxxh, String cfxh) {
        try {
            log.info("开始手动上传数据到两定接口平台，cfmxxh: {}, cfxh: {}", cfmxxh, cfxh);

            // 同步追溯码信息
            syncDrugTraceInfo();

            // 查询需要上传的数据
            List<Nhsa3505> dataList = queryDataToUpload(cfmxxh, cfxh);
            if (ObjectUtils.isEmpty(dataList)) {
                log.info("没有符合条件的数据需要上传");
                return ApiResult.success("没有符合条件的数据需要上传");
            }

            log.info("查询到符合条件的数据数量: {}", dataList.size());
            String signNo = NhsaHttpUtil.getSignNo(NhsaAccountConstant.getNhsaAccount());
            // 处理上传逻辑并返回结果
            return processUpload(dataList, true, signNo);

        } catch (Exception e) {
            log.error("手动上传数据到两定接口平台时发生异常", e);
            return ApiResult.error("手动上传失败: " + e.getMessage());
        }
    }

    /**
     * 同步发药明细表的追溯码信息到销售表
     */
    private void syncDrugTraceInfo() {
        List<YsfStoDpsSub> dpsSubList = ysfStoDpsSubMapper.getSendStatusList();
        log.info("查询可上传数据数量:{}", dpsSubList.size());

        for (YsfStoDpsSub sub : dpsSubList) {
            Nhsa3505 nhsa3505 = baseMapper.getByCfxhandDrugCode(sub.getCfxh(), sub.getDrugCode());
            if (!ObjectUtils.isEmpty(nhsa3505)) {
                if (ObjectUtils.isEmpty(nhsa3505.getDrugTracInfo())) {
                    nhsa3505.setDrugTracInfo(sub.getDrugtracinfo());
                    baseMapper.updateById(nhsa3505);
                }
            }
        }
    }

    /**
     * 查询需要上传的数据
     *
     * @param cfmxxh 处方明细序号，可为空
     * @param cfxh   处方序号，可为空
     * @return 符合条件的数据列表
     */
    private List<Nhsa3505> queryDataToUpload(String cfmxxh, String cfxh) {
        LambdaQueryWrapper<Nhsa3505> qw = new LambdaQueryWrapper<>();
        qw.eq(Nhsa3505::getHsaSyncStatus, "0"); // 未同步的数据
        // qw.eq(Nhsa3505::getMdtrtSetlType, "1"); // 只查询医保结算的数据
        qw.eq(Nhsa3505::getSdDps, SdDpsEnum.OUTPATIENT); // 只查询门诊的处方数据
        qw.ne(Nhsa3505::getDrugTracInfo, "");
        qw.isNotNull(Nhsa3505::getDrugTracInfo);
        qw.ne(Nhsa3505::getMdtrtSn, "");
        qw.isNotNull(Nhsa3505::getMdtrtSn);

        // 根据传入的参数进行条件筛选
        if (StringUtils.hasText(cfmxxh)) {
            qw.eq(Nhsa3505::getCfmxxh, cfmxxh);
            log.info("按处方明细序号筛选，cfmxxh: {}", cfmxxh);
        }
        if (StringUtils.hasText(cfxh)) {
            qw.eq(Nhsa3505::getCfxh, cfxh);
            log.info("按处方序号筛选，cfxh: {}", cfxh);
        }

        return baseMapper.selectList(qw);
    }

    /**
     * 构建Selinfo3505对象
     *
     * @param nhsa3505      源数据对象
     * @param lastYearToday 去年今天的日期（用于默认生产日期）
     * @param nextYearToday 明年今天的日期（用于默认过期日期）
     * @return 构建好的Selinfo3505对象
     */
    private Selinfo3505 buildSelinfo3505(Nhsa3505 nhsa3505, LocalDate lastYearToday, LocalDate nextYearToday) {
        Selinfo3505 selinfo3505 = new Selinfo3505();

        // 设置基本字段
        if (!ObjectUtils.isEmpty(nhsa3505.getMedListCodg())) {
            selinfo3505.setMed_list_codg(nhsa3505.getMedListCodg());
        } else {
            selinfo3505.setMed_list_codg("-");
        }

        selinfo3505.setFixmedins_hilist_id(nhsa3505.getFixmedinsHilistId());
        selinfo3505.setFixmedins_hilist_name(nhsa3505.getFixmedinsHilistName());
        selinfo3505.setFixmedins_bchno(nhsa3505.getFixmedinsBchno());
        selinfo3505.setPrsc_dr_cert_type(nhsa3505.getPrscDrCertType());
        selinfo3505.setPrsc_dr_certno(nhsa3505.getPrscDrCertno());
        selinfo3505.setPrsc_dr_name(nhsa3505.getPrscDrName());
        selinfo3505.setPhar_cert_type(nhsa3505.getPharCertType());
        selinfo3505.setPhar_certno(nhsa3505.getPharCertno());
        selinfo3505.setPhar_name(nhsa3505.getPharName());
        selinfo3505.setPhar_prac_cert_no(nhsa3505.getPharPracCertNo());
        selinfo3505.setHi_feesetl_type(nhsa3505.getHiFeesetlType());
        selinfo3505.setSetl_id(nhsa3505.getSetlId());
        selinfo3505.setMdtrt_sn(nhsa3505.getMdtrtSn());
        selinfo3505.setPsn_no(nhsa3505.getPsnNo());
        selinfo3505.setPsn_cert_type(nhsa3505.getPsnCertType());
        selinfo3505.setCertno(nhsa3505.getCertno());
        selinfo3505.setPsn_name(nhsa3505.getPsnName());
        selinfo3505.setManu_lotnum(nhsa3505.getManuLotnum());

        // 设置日期字段（带默认值）
        if (!ObjectUtils.isEmpty(nhsa3505.getManuDate())) {
            selinfo3505.setManu_date(nhsa3505.getManuDate());
        } else {
            selinfo3505.setManu_date(lastYearToday);
        }
        if (!ObjectUtils.isEmpty(nhsa3505.getExpyEnd())) {
            selinfo3505.setExpy_end(nhsa3505.getExpyEnd());
        } else {
            selinfo3505.setExpy_end(nextYearToday);
        }

        // 设置其他字段
        selinfo3505.setRx_flag(nhsa3505.getRxFlag());
        selinfo3505.setTrdn_flag(nhsa3505.getTrdnFlag());
        selinfo3505.setMemo(nhsa3505.getMemo());
        selinfo3505.setFinl_trns_pric(nhsa3505.getFinlTrnsPric());
        selinfo3505.setRxno(nhsa3505.getRxno());
        selinfo3505.setRx_circ_flag(nhsa3505.getRxCircFlag());
        selinfo3505.setRtal_docno(nhsa3505.getRtalDocno());
        selinfo3505.setStoout_no(nhsa3505.getStooutNo());
        selinfo3505.setBchno(nhsa3505.getBchno());
        selinfo3505.setDrug_prod_barc(nhsa3505.getDrugProdBarc());
        selinfo3505.setShelf_posi(nhsa3505.getShelfPosi());
        selinfo3505.setSel_retn_time(nhsa3505.getSelRetnTime());
        selinfo3505.setSel_retn_opter_name(nhsa3505.getSelRetnOpterName());
        selinfo3505.setMdtrt_setl_type(nhsa3505.getMdtrtSetlType());
        selinfo3505.setSel_retn_cnt(nhsa3505.getSelRetnCnt());

        // 设置追溯信息
        if (!ObjectUtils.isEmpty(nhsa3505.getDrugTracInfo())) {
            String[] split = nhsa3505.getDrugTracInfo()
                    .split(",");
            List<DrugTracInfo> drugtracinfo = Arrays.stream(split)
                    .map(a -> DrugTracInfo.builder()
                            .drug_trac_codg(a)
                            .build())
                    .collect(Collectors.toList());
            selinfo3505.setDrugtracinfo(drugtracinfo);
        }

        return selinfo3505;
    }

    /**
     * 处理数据上传到两定接口平台
     *
     * @param dataList 需要上传的数据列表
     * @param isManual 是否为手动上传
     * @return 如果是手动上传则返回ApiResult，否则返回null
     */
    public ApiResult<String> processUpload(List<Nhsa3505> dataList, boolean isManual, String signNo) {
        try {
            // 获取医保操作员的账号信息
            NhsaAccount nhsaAccount = NhsaAccountConstant.getNhsaAccount();

            // 获取今天的日期和默认日期
            LocalDate today = LocalDate.now();
            LocalDate lastYearToday = today.minusYears(1);
            LocalDate nextYearToday = today.plusYears(1);


            // 统计信息（用于手动上传）
            int successCount = 0;
            int failCount = 0;
            StringBuilder errorMessages = new StringBuilder();

            // 逐条处理数据
            for (Nhsa3505 nhsa3505 : dataList) {
                try {
                    // 构建请求对象
                    Selinfo3505 selinfo3505 = buildSelinfo3505(nhsa3505, lastYearToday, nextYearToday);

                    // 调用接口上传
                    NhsaCityResponse response = NhsaHttpUtil.fsi3505(signNo, selinfo3505, nhsaAccount);

                    // 处理响应结果
                    if (response.getBody()
                            .getInfcode() == 0) {
                        // 上传成功
                        String nowTime = LocalDateTime.now()
                                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                        String successRemark = isManual ? "手动上传两定接口成功 " + nowTime : "上传两定接口成功" + nowTime;

                        nhsa3505.setHsaSyncRemark(successRemark);
                        nhsa3505.setHsaSyncStatus("1");
                        nhsa3505.setHsaSyncTime(LocalDateTime.now());
                        nhsa3505.setDrugTracInfo(nhsa3505.getDrugTracInfo());
                        baseMapper.updateById(nhsa3505);

                        successCount++;
                        if (isManual) {
                            log.info("成功上传数据，cfmxxh: {}, cfxh: {}", nhsa3505.getCfmxxh(), nhsa3505.getCfxh());
                        }
                    } else {
                        // 上传失败
                        String errorMsg = "上传两定接口失败，错误内容：" + response.getBody()
                                .getErr_msg();
                        nhsa3505.setHsaSyncRemark(errorMsg);
                        nhsa3505.setHsaSyncStatus("2");
                        nhsa3505.setHsaSyncTime(LocalDateTime.now());
                        nhsa3505.setDrugTracInfo(nhsa3505.getDrugTracInfo());
                        baseMapper.updateById(nhsa3505);

                        failCount++;
                        if (isManual) {
                            errorMessages.append("cfmxxh: ")
                                    .append(nhsa3505.getCfmxxh())
                                    .append(", cfxh: ")
                                    .append(nhsa3505.getCfxh())
                                    .append(" - ")
                                    .append(response.getBody()
                                            .getErr_msg())
                                    .append("; ");
                            log.error("上传数据失败，cfmxxh: {}, cfxh: {}, 错误: {}",
                                    nhsa3505.getCfmxxh(), nhsa3505.getCfxh(), response.getBody()
                                            .getErr_msg());
                        }
                    }
                } catch (Exception e) {
                    failCount++;
                    if (isManual) {
                        String errorMsg = "处理数据时发生异常: " + e.getMessage();
                        errorMessages.append("cfmxxh: ")
                                .append(nhsa3505.getCfmxxh())
                                .append(", cfxh: ")
                                .append(nhsa3505.getCfxh())
                                .append(" - ")
                                .append(errorMsg)
                                .append("; ");
                        log.error("处理数据时发生异常，cfmxxh: {}, cfxh: {}", nhsa3505.getCfmxxh(), nhsa3505.getCfxh(), e);
                    } else {
                        log.error("处理数据时发生异常", e);
                    }
                }
            }

            // 返回结果（仅手动上传需要返回）
            if (isManual) {
                String resultMessage = String.format("手动上传完成。总数: %d, 成功: %d, 失败: %d",
                        dataList.size(), successCount, failCount);
                if (failCount > 0) {
                    resultMessage += "。失败详情: " + errorMessages;
                }

                log.info(resultMessage);

                if (failCount == 0) {
                    return ApiResult.success(resultMessage);
                } else if (successCount > 0) {
                    return ApiResult.success(resultMessage); // 部分成功也返回success，但包含失败信息
                } else {
                    return ApiResult.error(resultMessage);
                }
            } else {
                log.info("批量上传完成，总数: {}, 成功: {}, 失败: {}", dataList.size(), successCount, failCount);
            }

            return ApiResult.success();

        } catch (Exception e) {
            log.error("处理上传逻辑时发生异常", e);
            if (isManual) {
                return ApiResult.error("处理上传失败: " + e.getMessage());
            }
            throw e;
        }
    }

    @Override
    public void syncTaiHeDispenseTime() {
        // 查询任务
        List<YsfStoDpsTaskVo> taskList = ysfStoTcTaskMapper.selectDpsInfoByPendingTask();
        log.info("获取到待同步的任务数:{},详细数据:{}", taskList.size(), JSONUtil.toJsonStr(taskList));

        for (YsfStoDpsTaskVo task : taskList) {
            String cfxh = task.getCfxh();

            try {
                // 判断任务是否超过8小时，如果当前时间与任务创建时间的时间差大于8小时，则修改任务状态为已失效
                long hoursBetween = DateUtil.between(task.getCreateTime(), DateUtil.date(), DateUnit.HOUR);
                if (hoursBetween > 8) {
                    log.info("任务创建时间超过8小时, task: {}", task);
                    // 修改任务状态为已失效
                    ysfStoTcTaskService.lambdaUpdate()
                            .eq(YsfStoTcTask::getCdBiz, cfxh)
                            .eq(YsfStoTcTask::getDelFlag, "0")
                            .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.EXPIRED.getCode())
                            .update();
                    continue;
                }

                if ("1".equals(task.getSdDps())) {
                    // 住院处方 - 调用住院发药详情接口
                    syncInpatientDispenseTime(cfxh);
                } else {
                    // 门诊处方 - 调用门诊发药详情接口
                    syncOutpatientDispenseTime(cfxh);
                }

            } catch (Exception e) {
                log.error("同步发药时间异常，处方号：{}", cfxh, e);
            }
        }
    }

    /**
     * 同步住院发药时间
     * 参考queryInpatientPrescription方法逻辑
     *
     * @param cfxh 处方号
     */
    private void syncInpatientDispenseTime(String cfxh) {
        try {
            log.info("开始同步住院发药时间，处方号：{}", cfxh);

            // 构建查询参数 - 定时任务统一使用cfxh查询
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("record_id", cfxh);
            requestParams.put("fg_dps", "1"); // 只查询已发药的处方

            // 获取住院发药详情接口URL
            String requestUrl = hangChuangConfig.getInpatientDispenseDetailUrl();

            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "同步住院发药时间查询");

            // 解析响应结果
            List<InPatientDispenseDetailVo> dispenseList = HttpRequestUtil.parseResponseToList(responseBody, InPatientDispenseDetailVo.class);

            if (dispenseList != null && !dispenseList.isEmpty()) {
                // 查找匹配的发药记录
                for (InPatientDispenseDetailVo dispenseInfo : dispenseList) {
                    if (cfxh.equals(dispenseInfo.getRecordId()) && StrUtil.isNotEmpty(dispenseInfo.getSendTime())) {
                        String sendTime = dispenseInfo.getSendTime();
                        log.info("找到住院发药记录，处方号：{}，发药时间：{}", cfxh, sendTime);

                        // 更新发药表的发药时间和状态
                        ysfStoDpsService.lambdaUpdate()
                                .eq(YsfStoDps::getCfxh, cfxh)
                                .eq(YsfStoDps::getDelFlag, "0")
                                .set(YsfStoDps::getSendTime, DateUtils.parseDateTime(sendTime))
                                .set(YsfStoDps::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                                .update();

                        // 更新任务表状态为已完成
                        ysfStoTcTaskService.lambdaUpdate()
                                .eq(YsfStoTcTask::getCdBiz, cfxh)
                                .eq(YsfStoTcTask::getDelFlag, "0")
                                .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                                .update();

                        log.info("住院发药时间同步成功，处方号：{}", cfxh);
                        return;
                    }
                }
                log.warn("未找到匹配的住院发药记录，处方号：{}", cfxh);
            } else {
                log.warn("住院发药记录查询结果为空，处方号：{}", cfxh);
            }

        } catch (Exception e) {
            log.error("同步住院发药时间异常，处方号：{}", cfxh, e);
        }
    }

    /**
     * 同步门诊发药时间
     * 参考queryOutpatientPrescription方法逻辑
     *
     * @param cfxh 处方号
     */
    private void syncOutpatientDispenseTime(String cfxh) {
        try {
            log.info("开始同步门诊发药时间，处方号：{}", cfxh);

            // 构建查询参数 - 定时任务统一使用cfxh查询（cardType=4）
            Map<String, Object> requestParams = new HashMap<>();
            requestParams.put("cfxh", cfxh);

            // 获取门诊发药详情接口URL
            String requestUrl = hangChuangConfig.getOutpatientDispenseDetailUrl();

            // 发起HTTP请求
            String responseBody = HttpRequestUtil.executePostRequest(requestUrl, requestParams, "同步门诊发药时间查询");

            // 解析响应结果
            List<OutpatientPrescriptionVo> prescriptionList = HttpRequestUtil.parseResponseToList(responseBody, OutpatientPrescriptionVo.class);

            if (prescriptionList != null && !prescriptionList.isEmpty()) {
                // 查找匹配的发药记录
                for (OutpatientPrescriptionVo prescriptionInfo : prescriptionList) {
                    if (cfxh.equals(prescriptionInfo.getCfxh()) && StrUtil.isNotEmpty(prescriptionInfo.getSend_time())) {
                        String sendTime = prescriptionInfo.getSend_time();
                        log.info("找到门诊发药记录，处方号：{}，发药时间：{}", cfxh, sendTime);

                        // 更新发药表的发药时间和状态
                        ysfStoDpsService.lambdaUpdate()
                                .eq(YsfStoDps::getCfxh, cfxh)
                                .eq(YsfStoDps::getDelFlag, "0")
                                .set(YsfStoDps::getSendTime, DateUtils.parseDateTime(sendTime))
                                .set(YsfStoDps::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                                .update();

                        // 更新任务表状态为已完成
                        ysfStoTcTaskService.lambdaUpdate()
                                .eq(YsfStoTcTask::getCdBiz, cfxh)
                                .eq(YsfStoTcTask::getDelFlag, "0")
                                .set(YsfStoTcTask::getFgStatus, TaskStatusEnum.COMPLETED.getCode())
                                .update();

                        log.info("门诊发药时间同步成功，处方号：{}", cfxh);
                        return;
                    }
                }
                log.warn("未找到匹配的门诊发药记录，处方号：{}", cfxh);
            } else {
                log.warn("门诊发药记录查询结果为空，处方号：{}", cfxh);
            }

        } catch (Exception e) {
            log.error("同步门诊发药时间异常，处方号：{}", cfxh, e);
        }
    }

}
