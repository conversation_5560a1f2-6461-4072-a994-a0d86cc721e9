# 生产环境配置
spring:
  datasource:
    dynamic:
      datasource:
        # 主数据源 - 生产环境
        master:
          type: com.alibaba.druid.pool.DruidDataSource
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: ***************************************************************************************************************************************************
          username: taiherenyi
          password: rXBGp54HzcYewSpe
          # Druid 连接池配置 - 生产环境优化
          druid:
            # 初始连接数
            initial-size: 10
            # 最小连接池数量
            min-idle: 20
            # 最大连接池数量
            max-active: 50
            # 配置获取连接等待超时的时间
            max-wait: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            time-between-eviction-runs-millis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            min-evictable-idle-time-millis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            max-evictable-idle-time-millis: 900000
            # 配置检测连接是否有效
            validation-query: SELECT 1 FROM DUAL
            test-while-idle: true
            test-on-borrow: false
            test-on-return: false
            # 打开PSCache，并且指定每个连接上PSCache的大小
            pool-prepared-statements: true
            max-pool-prepared-statement-per-connection-size: 20
        # 从数据源1 - 读库
        # slave_1:
        #   type: com.alibaba.druid.pool.DruidDataSource
        #   driver-class-name: com.mysql.cj.jdbc.Driver
        #   url: *********************************************************************************************************************************************
        #   username: root
        #   password: 123456
        #   druid:
        #     initial-size: 5
        #     min-idle: 5
        #     max-active: 10
        #     max-wait: 60000
        #     time-between-eviction-runs-millis: 60000
        #     min-evictable-idle-time-millis: 300000
        #     max-evictable-idle-time-millis: 900000
        #     validation-query: SELECT 1 FROM DUAL
        #     test-while-idle: true
        #     test-on-borrow: false
        #     test-on-return: false
# Redis配置
  redis:
    # Redis连接地址
    host: localhost
    # Redis连接端口
    port: 6379
    # Redis数据库索引（默认为0）
    database: 7
    # Redis连接密码（如果设置了密码）
    password:
    # 连接超时时间（毫秒）
    timeout: 3000
    # Lettuce连接池配置
    lettuce:
      pool:
        # 连接池最大连接数（使用负值表示没有限制）
        max-active: 20
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 5
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
# 生产环境日志配置
logging:
  level:
    com.zsm.mapper: info
    com.baomidou.dynamic.datasource: info
    druid.sql.Statement: warn
    root: warn


# 生产环境 Swagger 配置
springdoc:
  swagger-ui:
    enabled: false  # 生产环境禁用 Swagger UI
  api-docs:
    enabled: false  # 生产环境禁用 API 文档

# 数据库初始化配置
app:
  database:
    auto-init: false  # 生产环境禁用自动初始化数据库表（手动执行SQL脚本）

# 杭创接口配置 - 生产环境
hangchuang:
  # 服务器地址配置
  server: http://172.31.1.159:30001
  # HIS接口服务器地址配置
  his-server: http://172.31.0.196:5000


